import 'package:flutter/material.dart';

import 'app_colors.dart';
import 'app_spacing.dart';
import 'app_typography.dart';

/// Extension on BuildContext to easily access theme values
extension ThemeExtension on BuildContext {
  /// Get the current theme data
  ThemeData get theme => Theme.of(this);

  /// Get the current color scheme
  ColorScheme get colorScheme => theme.colorScheme;

  /// Get the current text theme
  TextTheme get textTheme => theme.textTheme;

  /// Check if the current theme is dark
  bool get isDarkMode => theme.brightness == Brightness.dark;

  /// Get media query data
  MediaQueryData get mediaQuery => MediaQuery.of(this);

  /// Get screen size
  Size get screenSize => mediaQuery.size;

  /// Get screen width
  double get screenWidth => screenSize.width;

  /// Get screen height
  double get screenHeight => screenSize.height;

  /// Check if screen is small (phone)
  bool get isSmallScreen => screenWidth < 600;

  /// Check if screen is medium (tablet)
  bool get isMediumScreen => screenWidth >= 600 && screenWidth < 1200;

  /// Check if screen is large (desktop)
  bool get isLargeScreen => screenWidth >= 1200;
}

/// Extension on ColorScheme for custom colors
extension ColorSchemeExtension on ColorScheme {
  /// Success colors
  Color get success =>
      brightness == Brightness.light ? AppColors.success : AppColors.success;

  Color get onSuccess => brightness == Brightness.light
      ? AppColors.onSuccess
      : AppColors.onSuccess;

  Color get successContainer => brightness == Brightness.light
      ? AppColors.successContainer
      : AppColors.successContainer;

  Color get onSuccessContainer => brightness == Brightness.light
      ? AppColors.onSuccessContainer
      : AppColors.onSuccessContainer;

  /// Warning colors
  Color get warning =>
      brightness == Brightness.light ? AppColors.warning : AppColors.warning;

  Color get onWarning => brightness == Brightness.light
      ? AppColors.onWarning
      : AppColors.onWarning;

  Color get warningContainer => brightness == Brightness.light
      ? AppColors.warningContainer
      : AppColors.warningContainer;

  Color get onWarningContainer => brightness == Brightness.light
      ? AppColors.onWarningContainer
      : AppColors.onWarningContainer;

  /// Info colors
  Color get info =>
      brightness == Brightness.light ? AppColors.info : AppColors.info;

  Color get onInfo =>
      brightness == Brightness.light ? AppColors.onInfo : AppColors.onInfo;

  Color get infoContainer => brightness == Brightness.light
      ? AppColors.infoContainer
      : AppColors.infoContainer;

  Color get onInfoContainer => brightness == Brightness.light
      ? AppColors.onInfoContainer
      : AppColors.onInfoContainer;

  /// Level colors
  Color get platinum => AppColors.platinum;
  Color get gold => AppColors.gold;
  Color get silver => AppColors.silver;
  Color get bronze => AppColors.bronze;

  /// Status colors
  Color get active => AppColors.active;
  Color get inactive => AppColors.inactive;
  Color get pending => AppColors.pending;
  Color get completed => AppColors.completed;
  Color get cancelled => AppColors.cancelled;

  /// Chart colors
  List<Color> get chartColors => AppColors.chartColors;
}

/// Extension on TextTheme for custom text styles
extension TextThemeExtension on TextTheme {
  /// Numeric text styles
  TextStyle get numericLarge => AppTypography.numericLarge;
  TextStyle get numericMedium => AppTypography.numericMedium;
  TextStyle get numericSmall => AppTypography.numericSmall;

  /// Custom text styles
  TextStyle get caption => AppTypography.caption;
  TextStyle get overline => AppTypography.overline;
  TextStyle get button => AppTypography.button;
}

/// Custom theme data helper
class AppThemeData {
  const AppThemeData({required this.spacing, required this.customColors});

  final AppSpacingData spacing;
  final AppCustomColors customColors;
}

/// Custom spacing data
class AppSpacingData {
  const AppSpacingData();

  double get xs => AppSpacing.xs;
  double get sm => AppSpacing.sm;
  double get md => AppSpacing.md;
  double get lg => AppSpacing.lg;
  double get xl => AppSpacing.xl;
  double get xxl => AppSpacing.xxl;
  double get xxxl => AppSpacing.xxxl;

  EdgeInsets get paddingXS => AppSpacing.paddingXS;
  EdgeInsets get paddingSM => AppSpacing.paddingSM;
  EdgeInsets get paddingMD => AppSpacing.paddingMD;
  EdgeInsets get paddingLG => AppSpacing.paddingLG;
  EdgeInsets get paddingXL => AppSpacing.paddingXL;
  EdgeInsets get paddingXXL => AppSpacing.paddingXXL;

  BorderRadius get borderRadiusXS => AppSpacing.borderRadiusXS;
  BorderRadius get borderRadiusSM => AppSpacing.borderRadiusSM;
  BorderRadius get borderRadiusMD => AppSpacing.borderRadiusMD;
  BorderRadius get borderRadiusLG => AppSpacing.borderRadiusLG;
  BorderRadius get borderRadiusXL => AppSpacing.borderRadiusXL;
  BorderRadius get borderRadiusXXL => AppSpacing.borderRadiusXXL;
}

/// Custom colors data
class AppCustomColors {
  const AppCustomColors({
    required this.success,
    required this.warning,
    required this.info,
    required this.platinum,
    required this.gold,
    required this.silver,
    required this.bronze,
  });

  final Color success;
  final Color warning;
  final Color info;
  final Color platinum;
  final Color gold;
  final Color silver;
  final Color bronze;
}
