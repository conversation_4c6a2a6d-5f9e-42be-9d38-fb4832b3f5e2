import 'package:drift/drift.dart';

/// Orders table definition for tracking daily order statistics and performance.
/// Stores information about completed, missed, and canceled orders.
@DataClassName('OrdersData')
class OrdersTable extends Table {
  /// Primary key UUID
  TextColumn get uuid => text()();

  /// Auto-increment ID for internal use
  IntColumn get id => integer().autoIncrement()();

  /// Date of the order record
  DateTimeColumn get date => dateTime()();

  /// Number of completed orders
  IntColumn get orderCompleted => integer()();

  /// Number of missed orders
  IntColumn get orderMissed => integer()();

  /// Number of canceled orders
  IntColumn get orderCanceled => integer()();

  /// Number of CBS (Call Back System) orders
  IntColumn get cbsOrder => integer()();

  /// Total incoming orders (calculated)
  IntColumn get incomingOrder => integer().nullable()();

  /// Orders received (calculated)
  IntColumn get orderReceived => integer().nullable()();

  /// Bid acceptance rate (calculated)
  RealColumn get bidAcceptance => real().nullable()();

  /// Trip completion rate (calculated)
  RealColumn get tripCompletion => real().nullable()();

  /// Points earned for the day
  IntColumn get points => integer()();

  /// Trip earnings
  RealColumn get trip => real()();

  /// Bonus amount
  RealColumn get bonus => real()();

  /// Tips received
  RealColumn get tips => real()();

  /// Total income (calculated)
  RealColumn get income => real().nullable()();

  /// Record creation timestamp
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();

  /// Record update timestamp
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();

  /// Soft delete timestamp
  DateTimeColumn get deletedAt => dateTime().nullable()();

  /// Sync status for cloud synchronization
  TextColumn get syncStatus => text().withDefault(const Constant('pendingUpload'))();

  @override
  Set<Column> get primaryKey => {uuid};

  @override
  List<String> get customConstraints => [
    'CHECK (order_completed >= 0)',
    'CHECK (order_missed >= 0)',
    'CHECK (order_canceled >= 0)',
    'CHECK (cbs_order >= 0)',
    'CHECK (points >= 0)',
    'CHECK (trip >= 0)',
    'CHECK (bonus >= 0)',
    'CHECK (tips >= 0)',
  ];
}
