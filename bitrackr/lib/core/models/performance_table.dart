import 'package:drift/drift.dart';

/// Performance table definition for tracking driver performance analytics.
/// Stores information about bid performance, trip performance, and activity metrics.
@DataClassName('PerformanceData')
class PerformanceTable extends Table {
  /// Primary key UUID
  TextColumn get uuid => text()();

  /// Auto-increment ID for internal use
  IntColumn get id => integer().autoIncrement()();

  /// Date of the performance record
  DateTimeColumn get date => dateTime()();

  /// Bid performance percentage
  RealColumn get bidPerformance => real()();

  /// Trip performance percentage
  RealColumn get tripPerformance => real()();

  /// Number of active days
  IntColumn get activeDays => integer()();

  /// Total online hours
  RealColumn get onlineHours => real()();

  /// Average completed orders per day (calculated)
  RealColumn get avgCompleted => real().nullable()();

  /// Average online hours per day (calculated)
  RealColumn get avgOnline => real().nullable()();

  /// Retention rate (calculated)
  RealColumn get retention => real().nullable()();

  /// Record creation timestamp
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();

  /// Record update timestamp
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();

  /// Soft delete timestamp
  DateTimeColumn get deletedAt => dateTime().nullable()();

  /// Sync status for cloud synchronization
  TextColumn get syncStatus => text().withDefault(const Constant('pendingUpload'))();

  @override
  Set<Column> get primaryKey => {uuid};

  @override
  List<String> get customConstraints => [
    'CHECK (bid_performance >= 0 AND bid_performance <= 100)',
    'CHECK (trip_performance >= 0 AND trip_performance <= 100)',
    'CHECK (active_days >= 0)',
    'CHECK (online_hours >= 0)',
  ];
}
