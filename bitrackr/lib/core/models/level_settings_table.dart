import 'package:drift/drift.dart';

/// Level Settings table definition for configuring driver level requirements.
/// Stores the requirements for each level (Platinum, Gold, Silver).
@DataClassName('LevelSettingsData')
class LevelSettingsTable extends Table {
  /// Auto-increment ID for internal use
  IntColumn get id => integer().autoIncrement()();

  /// Points required for Platinum level
  IntColumn get platinumPointsReq => integer()();

  /// Bid acceptance rate required for Platinum level
  RealColumn get platinumBidReq => real()();

  /// Trip completion rate required for Platinum level
  RealColumn get platinumTripReq => real()();

  /// Points required for Gold level
  IntColumn get goldPointsReq => integer()();

  /// Bid acceptance rate required for Gold level
  RealColumn get goldBidReq => real()();

  /// Trip completion rate required for Gold level
  RealColumn get goldTripReq => real()();

  /// Points required for Silver level
  IntColumn get silverPointsReq => integer()();

  /// Bid acceptance rate required for Silver level
  RealColumn get silverBidReq => real()();

  /// Trip completion rate required for Silver level
  RealColumn get silverTripReq => real()();

  @override
  Set<Column> get primaryKey => {id};

  @override
  List<String> get customConstraints => [
    'CHECK (platinum_points_req >= gold_points_req)',
    'CHECK (gold_points_req >= silver_points_req)',
    'CHECK (silver_points_req >= 0)',
    'CHECK (platinum_bid_req >= 0 AND platinum_bid_req <= 1)',
    'CHECK (gold_bid_req >= 0 AND gold_bid_req <= 1)',
    'CHECK (silver_bid_req >= 0 AND silver_bid_req <= 1)',
    'CHECK (platinum_trip_req >= 0 AND platinum_trip_req <= 1)',
    'CHECK (gold_trip_req >= 0 AND gold_trip_req <= 1)',
    'CHECK (silver_trip_req >= 0 AND silver_trip_req <= 1)',
  ];
}
