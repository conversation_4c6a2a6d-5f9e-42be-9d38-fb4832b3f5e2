import 'package:drift/drift.dart';

/// App Settings table definition for storing application configuration.
/// Stores global app settings like date ranges and backup preferences.
@DataClassName('AppSettingsData')
class AppSettingsTable extends Table {
  /// Auto-increment ID for internal use
  IntColumn get id => integer().autoIncrement()();

  /// Start date for default date range filtering
  DateTimeColumn get dateRangeStart => dateTime()();

  /// End date for default date range filtering
  DateTimeColumn get dateRangeEnd => dateTime()();

  /// Path to backup directory
  TextColumn get backupDirectoryPath => text().nullable()();

  /// Last update timestamp
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();

  /// Last synchronization timestamp
  DateTimeColumn get lastSyncTime => dateTime().nullable()();

  @override
  Set<Column> get primaryKey => {id};

  @override
  List<String> get customConstraints => [
    'CHECK (date_range_end >= date_range_start)',
  ];
}
