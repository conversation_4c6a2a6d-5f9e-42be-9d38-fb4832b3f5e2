import 'package:drift/drift.dart';

/// Spare Parts table definition for tracking vehicle maintenance and parts.
/// Stores information about installed parts, replacement schedules, and usage.
@DataClassName('SparePartsData')
class SparePartsTable extends Table {
  /// Primary key UUID
  TextColumn get uuid => text()();

  /// Auto-increment ID for internal use
  IntColumn get id => integer().autoIncrement()();

  /// Name of the spare part
  TextColumn get partName => text()();

  /// Type/category of the spare part
  TextColumn get partType => text()();

  /// Price of the spare part
  RealColumn get price => real()();

  /// Mileage limit for replacement
  IntColumn get mileageLimit => integer()();

  /// Initial mileage when part was installed
  IntColumn get initialMileage => integer()();

  /// Installation date
  DateTimeColumn get installationDate => dateTime().withDefault(currentDateAndTime)();

  /// Current vehicle mileage
  IntColumn get currentMileage => integer().withDefault(const Constant(0))();

  /// Warning status for maintenance alerts
  BoolColumn get warningStatus => boolean().withDefault(const Constant(false))();

  /// Number of times this part has been replaced
  IntColumn get replacementCount => integer().withDefault(const Constant(0))();

  /// Additional notes about the part
  TextColumn get notes => text().withDefault(const Constant(''))();

  /// Record creation timestamp
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();

  /// Record update timestamp
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();

  /// Soft delete timestamp
  DateTimeColumn get deletedAt => dateTime().nullable()();

  /// Sync status for cloud synchronization
  TextColumn get syncStatus => text().withDefault(const Constant('pendingUpload'))();

  @override
  Set<Column> get primaryKey => {uuid};

  @override
  List<String> get customConstraints => [
    'CHECK (price >= 0)',
    'CHECK (mileage_limit > 0)',
    'CHECK (initial_mileage >= 0)',
    'CHECK (current_mileage >= initial_mileage)',
    'CHECK (replacement_count >= 0)',
  ];
}
