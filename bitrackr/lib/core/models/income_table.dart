import 'package:drift/drift.dart';

/// Income table definition for tracking daily earnings and mileage.
/// Stores information about payment methods, mileage, and calculated values.
@DataClassName('IncomeData')
class IncomeTable extends Table {
  /// Primary key UUID
  TextColumn get uuid => text()();

  /// Auto-increment ID for internal use
  IntColumn get id => integer().autoIncrement()();

  /// Date of the income record
  DateTimeColumn get date => dateTime()();

  /// Initial mileage reading
  IntColumn get initialMileage => integer()();

  /// Final mileage reading
  IntColumn get finalMileage => integer()();

  /// Initial GoPay amount
  RealColumn get initialGopay => real()();

  /// Initial BCA amount
  RealColumn get initialBca => real()();

  /// Initial Cash amount
  RealColumn get initialCash => real()();

  /// Initial OVO amount
  RealColumn get initialOvo => real()();

  /// Initial BRI amount
  RealColumn get initialBri => real()();

  /// Initial RekPon amount
  RealColumn get initialRekpon => real()();

  /// Final GoPay amount
  RealColumn get finalGopay => real()();

  /// Final BCA amount
  RealColumn get finalBca => real()();

  /// Final Cash amount
  RealColumn get finalCash => real()();

  /// Final OVO amount
  RealColumn get finalOvo => real()();

  /// Final BRI amount
  RealColumn get finalBri => real()();

  /// Final RekPon amount
  RealColumn get finalRekpon => real()();

  /// Initial capital (calculated)
  RealColumn get initialCapital => real().nullable()();

  /// Final result (calculated)
  RealColumn get finalResult => real().nullable()();

  /// Mileage driven (calculated)
  IntColumn get mileage => integer().nullable()();

  /// Net income (calculated)
  RealColumn get netIncome => real().nullable()();

  /// Record creation timestamp
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();

  /// Record update timestamp
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();

  /// Soft delete timestamp
  DateTimeColumn get deletedAt => dateTime().nullable()();

  /// Sync status for cloud synchronization
  TextColumn get syncStatus => text().withDefault(const Constant('pendingUpload'))();

  @override
  Set<Column> get primaryKey => {uuid};

  @override
  List<String> get customConstraints => [
    'CHECK (final_mileage >= initial_mileage)',
    'CHECK (initial_gopay >= 0)',
    'CHECK (initial_bca >= 0)',
    'CHECK (initial_cash >= 0)',
    'CHECK (initial_ovo >= 0)',
    'CHECK (initial_bri >= 0)',
    'CHECK (initial_rekpon >= 0)',
    'CHECK (final_gopay >= 0)',
    'CHECK (final_bca >= 0)',
    'CHECK (final_cash >= 0)',
    'CHECK (final_ovo >= 0)',
    'CHECK (final_bri >= 0)',
    'CHECK (final_rekpon >= 0)',
  ];
}
