import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Service locator pattern implementation for dependency injection.
/// Provides a centralized way to register and resolve dependencies.
class ServiceLocator {
  ServiceLocator._();

  static ServiceLocator? _instance;
  static ServiceLocator get instance => _instance ??= ServiceLocator._();

  final Map<Type, dynamic> _services = {};
  final Map<Type, dynamic Function()> _factories = {};
  final Map<Type, dynamic Function()> _singletons = {};
  final Map<Type, dynamic> _singletonInstances = {};

  /// Register a service instance
  void registerInstance<T>(T instance) {
    _services[T] = instance;
  }

  /// Register a factory function for creating instances
  void registerFactory<T>(T Function() factory) {
    _factories[T] = factory;
  }

  /// Register a singleton factory function
  void registerSingleton<T>(T Function() factory) {
    _singletons[T] = factory;
  }

  /// Get a service instance
  T get<T>() {
    // Check if instance is already registered
    if (_services.containsKey(T)) {
      return _services[T] as T;
    }

    // Check if singleton instance exists
    if (_singletonInstances.containsKey(T)) {
      return _singletonInstances[T] as T;
    }

    // Create singleton instance if factory exists
    if (_singletons.containsKey(T)) {
      final instance = _singletons[T]!() as T;
      _singletonInstances[T] = instance;
      return instance;
    }

    // Create instance from factory
    if (_factories.containsKey(T)) {
      return _factories[T]!() as T;
    }

    throw Exception('Service of type $T is not registered');
  }

  /// Check if a service is registered
  bool isRegistered<T>() {
    return _services.containsKey(T) ||
        _factories.containsKey(T) ||
        _singletons.containsKey(T);
  }

  /// Unregister a service
  void unregister<T>() {
    _services.remove(T);
    _factories.remove(T);
    _singletons.remove(T);
    _singletonInstances.remove(T);
  }

  /// Clear all registered services
  void clear() {
    _services.clear();
    _factories.clear();
    _singletons.clear();
    _singletonInstances.clear();
  }

  /// Reset singleton instances (useful for testing)
  void resetSingletons() {
    _singletonInstances.clear();
  }

  /// Get all registered service types
  List<Type> get registeredTypes => [
        ..._services.keys,
        ..._factories.keys,
        ..._singletons.keys,
      ];

  /// Get service registration info for debugging
  Map<String, dynamic> get registrationInfo => {
        'instances': _services.keys.map((e) => e.toString()).toList(),
        'factories': _factories.keys.map((e) => e.toString()).toList(),
        'singletons': _singletons.keys.map((e) => e.toString()).toList(),
        'singletonInstances': _singletonInstances.keys.map((e) => e.toString()).toList(),
      };
}

/// Extension to make service locator usage more convenient
extension ServiceLocatorExtension on ServiceLocator {
  /// Register multiple services at once
  void registerAll(Map<Type, dynamic> services) {
    for (final entry in services.entries) {
      _services[entry.key] = entry.value;
    }
  }

  /// Register multiple factories at once
  void registerFactories(Map<Type, dynamic Function()> factories) {
    for (final entry in factories.entries) {
      _factories[entry.key] = entry.value;
    }
  }

  /// Register multiple singletons at once
  void registerSingletons(Map<Type, dynamic Function()> singletons) {
    for (final entry in singletons.entries) {
      _singletons[entry.key] = entry.value;
    }
  }

  /// Try to get a service, return null if not found
  T? tryGet<T>() {
    try {
      return get<T>();
    } catch (e) {
      return null;
    }
  }

  /// Get a service with a default value if not found
  T getOrDefault<T>(T defaultValue) {
    try {
      return get<T>();
    } catch (e) {
      return defaultValue;
    }
  }
}

/// Provider for the service locator
final serviceLocatorProvider = Provider<ServiceLocator>((ref) {
  return ServiceLocator.instance;
});

/// Mixin for classes that need access to the service locator
mixin ServiceLocatorMixin {
  ServiceLocator get serviceLocator => ServiceLocator.instance;

  T getService<T>() => serviceLocator.get<T>();
  T? tryGetService<T>() => serviceLocator.tryGet<T>();
  T getServiceOrDefault<T>(T defaultValue) => serviceLocator.getOrDefault<T>(defaultValue);
}

/// Helper class for setting up common services
class ServiceSetup {
  ServiceSetup._();

  /// Setup core services for the application
  static void setupCoreServices() {
    final locator = ServiceLocator.instance;

    // Register core services here when they are created
    // Example:
    // locator.registerSingleton<DatabaseService>(() => DatabaseService());
    // locator.registerSingleton<ApiService>(() => ApiService());
  }

  /// Setup test services for testing
  static void setupTestServices() {
    final locator = ServiceLocator.instance;
    
    // Clear existing services
    locator.clear();

    // Register test services here
    // Example:
    // locator.registerInstance<DatabaseService>(MockDatabaseService());
    // locator.registerInstance<ApiService>(MockApiService());
  }

  /// Setup development services for development
  static void setupDevelopmentServices() {
    final locator = ServiceLocator.instance;

    // Register development-specific services here
    // Example:
    // locator.registerSingleton<LoggingService>(() => VerboseLoggingService());
  }
}
