import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

/// Application configuration class that handles environment variables
/// and app-wide configuration settings.
class AppConfig {
  AppConfig._();

  static AppConfig? _instance;
  static AppConfig get instance => _instance ??= AppConfig._();

  // Environment variables
  String? _supabaseUrl;
  String? _supabaseAnonKey;
  String? _supabaseServiceRoleKey;
  String? _appName;
  String? _appVersion;
  String? _databaseName;
  bool? _debugMode;
  bool? _enableLogging;

  /// Initialize the app configuration by loading environment variables
  Future<void> initialize() async {
    try {
      // Load environment variables from .env file
      final envString = await rootBundle.loadString('.env');
      final envMap = _parseEnvString(envString);

      _supabaseUrl = envMap['SUPABASE_URL'];
      _supabaseAnonKey = envMap['SUPABASE_ANON_KEY'];
      _supabaseServiceRoleKey = envMap['SUPABASE_SERVICE_ROLE_KEY'];
      _appName = envMap['APP_NAME'] ?? 'bitrackr';
      _appVersion = envMap['APP_VERSION'] ?? '1.0.0';
      _databaseName = envMap['DATABASE_NAME'] ?? 'bitrackr.db';
      _debugMode = _parseBool(envMap['DEBUG_MODE']) ?? kDebugMode;
      _enableLogging = _parseBool(envMap['ENABLE_LOGGING']) ?? true;
    } catch (e) {
      // If .env file doesn't exist or can't be loaded, use default values
      debugPrint('Warning: Could not load .env file. Using default values.');
      _setDefaultValues();
    }
  }

  /// Parse environment string into a map
  Map<String, String> _parseEnvString(String envString) {
    final envMap = <String, String>{};
    final lines = envString.split('\n');

    for (final line in lines) {
      final trimmedLine = line.trim();
      if (trimmedLine.isEmpty || trimmedLine.startsWith('#')) {
        continue;
      }

      final parts = trimmedLine.split('=');
      if (parts.length >= 2) {
        final key = parts[0].trim();
        final value = parts.sublist(1).join('=').trim();
        envMap[key] = value;
      }
    }

    return envMap;
  }

  /// Parse boolean value from string
  bool? _parseBool(String? value) {
    if (value == null) return null;
    return value.toLowerCase() == 'true';
  }

  /// Set default configuration values
  void _setDefaultValues() {
    _appName = 'bitrackr';
    _appVersion = '1.0.0';
    _databaseName = 'bitrackr.db';
    _debugMode = kDebugMode;
    _enableLogging = true;
  }

  // Getters for configuration values
  String get supabaseUrl {
    if (_supabaseUrl == null || _supabaseUrl!.isEmpty) {
      throw Exception('SUPABASE_URL not configured. Please check your .env file.');
    }
    return _supabaseUrl!;
  }

  String get supabaseAnonKey {
    if (_supabaseAnonKey == null || _supabaseAnonKey!.isEmpty) {
      throw Exception('SUPABASE_ANON_KEY not configured. Please check your .env file.');
    }
    return _supabaseAnonKey!;
  }

  String? get supabaseServiceRoleKey => _supabaseServiceRoleKey;

  String get appName => _appName ?? 'bitrackr';

  String get appVersion => _appVersion ?? '1.0.0';

  String get databaseName => _databaseName ?? 'bitrackr.db';

  bool get debugMode => _debugMode ?? kDebugMode;

  bool get enableLogging => _enableLogging ?? true;

  /// Check if Supabase is properly configured
  bool get isSupabaseConfigured =>
      _supabaseUrl != null &&
      _supabaseUrl!.isNotEmpty &&
      _supabaseAnonKey != null &&
      _supabaseAnonKey!.isNotEmpty;

  /// Get configuration summary for debugging
  Map<String, dynamic> get configSummary => {
        'appName': appName,
        'appVersion': appVersion,
        'databaseName': databaseName,
        'debugMode': debugMode,
        'enableLogging': enableLogging,
        'isSupabaseConfigured': isSupabaseConfigured,
      };
}
