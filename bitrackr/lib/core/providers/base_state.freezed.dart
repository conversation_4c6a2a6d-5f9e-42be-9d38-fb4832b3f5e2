// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'base_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$BaseState<T> {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(T data) success,
    required TResult Function(Failure failure) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(T data)? success,
    TResult? Function(Failure failure)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(T data)? success,
    TResult Function(Failure failure)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial<T> value) initial,
    required TResult Function(Loading<T> value) loading,
    required TResult Function(Success<T> value) success,
    required TResult Function(Error<T> value) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial<T> value)? initial,
    TResult? Function(Loading<T> value)? loading,
    TResult? Function(Success<T> value)? success,
    TResult? Function(Error<T> value)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial<T> value)? initial,
    TResult Function(Loading<T> value)? loading,
    TResult Function(Success<T> value)? success,
    TResult Function(Error<T> value)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BaseStateCopyWith<T, $Res> {
  factory $BaseStateCopyWith(
    BaseState<T> value,
    $Res Function(BaseState<T>) then,
  ) = _$BaseStateCopyWithImpl<T, $Res, BaseState<T>>;
}

/// @nodoc
class _$BaseStateCopyWithImpl<T, $Res, $Val extends BaseState<T>>
    implements $BaseStateCopyWith<T, $Res> {
  _$BaseStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<T, $Res> {
  factory _$$InitialImplCopyWith(
    _$InitialImpl<T> value,
    $Res Function(_$InitialImpl<T>) then,
  ) = __$$InitialImplCopyWithImpl<T, $Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<T, $Res>
    extends _$BaseStateCopyWithImpl<T, $Res, _$InitialImpl<T>>
    implements _$$InitialImplCopyWith<T, $Res> {
  __$$InitialImplCopyWithImpl(
    _$InitialImpl<T> _value,
    $Res Function(_$InitialImpl<T>) _then,
  ) : super(_value, _then);

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl<T> implements Initial<T> {
  const _$InitialImpl();

  @override
  String toString() {
    return 'BaseState<$T>.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl<T>);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(T data) success,
    required TResult Function(Failure failure) error,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(T data)? success,
    TResult? Function(Failure failure)? error,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(T data)? success,
    TResult Function(Failure failure)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial<T> value) initial,
    required TResult Function(Loading<T> value) loading,
    required TResult Function(Success<T> value) success,
    required TResult Function(Error<T> value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial<T> value)? initial,
    TResult? Function(Loading<T> value)? loading,
    TResult? Function(Success<T> value)? success,
    TResult? Function(Error<T> value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial<T> value)? initial,
    TResult Function(Loading<T> value)? loading,
    TResult Function(Success<T> value)? success,
    TResult Function(Error<T> value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class Initial<T> implements BaseState<T> {
  const factory Initial() = _$InitialImpl<T>;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<T, $Res> {
  factory _$$LoadingImplCopyWith(
    _$LoadingImpl<T> value,
    $Res Function(_$LoadingImpl<T>) then,
  ) = __$$LoadingImplCopyWithImpl<T, $Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<T, $Res>
    extends _$BaseStateCopyWithImpl<T, $Res, _$LoadingImpl<T>>
    implements _$$LoadingImplCopyWith<T, $Res> {
  __$$LoadingImplCopyWithImpl(
    _$LoadingImpl<T> _value,
    $Res Function(_$LoadingImpl<T>) _then,
  ) : super(_value, _then);

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl<T> implements Loading<T> {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'BaseState<$T>.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl<T>);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(T data) success,
    required TResult Function(Failure failure) error,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(T data)? success,
    TResult? Function(Failure failure)? error,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(T data)? success,
    TResult Function(Failure failure)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial<T> value) initial,
    required TResult Function(Loading<T> value) loading,
    required TResult Function(Success<T> value) success,
    required TResult Function(Error<T> value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial<T> value)? initial,
    TResult? Function(Loading<T> value)? loading,
    TResult? Function(Success<T> value)? success,
    TResult? Function(Error<T> value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial<T> value)? initial,
    TResult Function(Loading<T> value)? loading,
    TResult Function(Success<T> value)? success,
    TResult Function(Error<T> value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class Loading<T> implements BaseState<T> {
  const factory Loading() = _$LoadingImpl<T>;
}

/// @nodoc
abstract class _$$SuccessImplCopyWith<T, $Res> {
  factory _$$SuccessImplCopyWith(
    _$SuccessImpl<T> value,
    $Res Function(_$SuccessImpl<T>) then,
  ) = __$$SuccessImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({T data});
}

/// @nodoc
class __$$SuccessImplCopyWithImpl<T, $Res>
    extends _$BaseStateCopyWithImpl<T, $Res, _$SuccessImpl<T>>
    implements _$$SuccessImplCopyWith<T, $Res> {
  __$$SuccessImplCopyWithImpl(
    _$SuccessImpl<T> _value,
    $Res Function(_$SuccessImpl<T>) _then,
  ) : super(_value, _then);

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? data = freezed}) {
    return _then(
      _$SuccessImpl<T>(
        freezed == data
            ? _value.data
            : data // ignore: cast_nullable_to_non_nullable
                  as T,
      ),
    );
  }
}

/// @nodoc

class _$SuccessImpl<T> implements Success<T> {
  const _$SuccessImpl(this.data);

  @override
  final T data;

  @override
  String toString() {
    return 'BaseState<$T>.success(data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SuccessImpl<T> &&
            const DeepCollectionEquality().equals(other.data, data));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(data));

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SuccessImplCopyWith<T, _$SuccessImpl<T>> get copyWith =>
      __$$SuccessImplCopyWithImpl<T, _$SuccessImpl<T>>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(T data) success,
    required TResult Function(Failure failure) error,
  }) {
    return success(data);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(T data)? success,
    TResult? Function(Failure failure)? error,
  }) {
    return success?.call(data);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(T data)? success,
    TResult Function(Failure failure)? error,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(data);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial<T> value) initial,
    required TResult Function(Loading<T> value) loading,
    required TResult Function(Success<T> value) success,
    required TResult Function(Error<T> value) error,
  }) {
    return success(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial<T> value)? initial,
    TResult? Function(Loading<T> value)? loading,
    TResult? Function(Success<T> value)? success,
    TResult? Function(Error<T> value)? error,
  }) {
    return success?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial<T> value)? initial,
    TResult Function(Loading<T> value)? loading,
    TResult Function(Success<T> value)? success,
    TResult Function(Error<T> value)? error,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(this);
    }
    return orElse();
  }
}

abstract class Success<T> implements BaseState<T> {
  const factory Success(final T data) = _$SuccessImpl<T>;

  T get data;

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SuccessImplCopyWith<T, _$SuccessImpl<T>> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ErrorImplCopyWith<T, $Res> {
  factory _$$ErrorImplCopyWith(
    _$ErrorImpl<T> value,
    $Res Function(_$ErrorImpl<T>) then,
  ) = __$$ErrorImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({Failure failure});

  $FailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$ErrorImplCopyWithImpl<T, $Res>
    extends _$BaseStateCopyWithImpl<T, $Res, _$ErrorImpl<T>>
    implements _$$ErrorImplCopyWith<T, $Res> {
  __$$ErrorImplCopyWithImpl(
    _$ErrorImpl<T> _value,
    $Res Function(_$ErrorImpl<T>) _then,
  ) : super(_value, _then);

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? failure = null}) {
    return _then(
      _$ErrorImpl<T>(
        null == failure
            ? _value.failure
            : failure // ignore: cast_nullable_to_non_nullable
                  as Failure,
      ),
    );
  }

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FailureCopyWith<$Res> get failure {
    return $FailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$ErrorImpl<T> implements Error<T> {
  const _$ErrorImpl(this.failure);

  @override
  final Failure failure;

  @override
  String toString() {
    return 'BaseState<$T>.error(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorImpl<T> &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ErrorImplCopyWith<T, _$ErrorImpl<T>> get copyWith =>
      __$$ErrorImplCopyWithImpl<T, _$ErrorImpl<T>>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(T data) success,
    required TResult Function(Failure failure) error,
  }) {
    return error(failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(T data)? success,
    TResult? Function(Failure failure)? error,
  }) {
    return error?.call(failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(T data)? success,
    TResult Function(Failure failure)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial<T> value) initial,
    required TResult Function(Loading<T> value) loading,
    required TResult Function(Success<T> value) success,
    required TResult Function(Error<T> value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial<T> value)? initial,
    TResult? Function(Loading<T> value)? loading,
    TResult? Function(Success<T> value)? success,
    TResult? Function(Error<T> value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial<T> value)? initial,
    TResult Function(Loading<T> value)? loading,
    TResult Function(Success<T> value)? success,
    TResult Function(Error<T> value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class Error<T> implements BaseState<T> {
  const factory Error(final Failure failure) = _$ErrorImpl<T>;

  Failure get failure;

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ErrorImplCopyWith<T, _$ErrorImpl<T>> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$PaginatedState<T> {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<T> currentData) loadingMore,
    required TResult Function(List<T> data, bool hasMore, int currentPage)
    success,
    required TResult Function(Failure failure) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<T> currentData)? loadingMore,
    TResult? Function(List<T> data, bool hasMore, int currentPage)? success,
    TResult? Function(Failure failure)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<T> currentData)? loadingMore,
    TResult Function(List<T> data, bool hasMore, int currentPage)? success,
    TResult Function(Failure failure)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PaginatedInitial<T> value) initial,
    required TResult Function(PaginatedLoading<T> value) loading,
    required TResult Function(PaginatedLoadingMore<T> value) loadingMore,
    required TResult Function(PaginatedSuccess<T> value) success,
    required TResult Function(PaginatedError<T> value) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PaginatedInitial<T> value)? initial,
    TResult? Function(PaginatedLoading<T> value)? loading,
    TResult? Function(PaginatedLoadingMore<T> value)? loadingMore,
    TResult? Function(PaginatedSuccess<T> value)? success,
    TResult? Function(PaginatedError<T> value)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PaginatedInitial<T> value)? initial,
    TResult Function(PaginatedLoading<T> value)? loading,
    TResult Function(PaginatedLoadingMore<T> value)? loadingMore,
    TResult Function(PaginatedSuccess<T> value)? success,
    TResult Function(PaginatedError<T> value)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaginatedStateCopyWith<T, $Res> {
  factory $PaginatedStateCopyWith(
    PaginatedState<T> value,
    $Res Function(PaginatedState<T>) then,
  ) = _$PaginatedStateCopyWithImpl<T, $Res, PaginatedState<T>>;
}

/// @nodoc
class _$PaginatedStateCopyWithImpl<T, $Res, $Val extends PaginatedState<T>>
    implements $PaginatedStateCopyWith<T, $Res> {
  _$PaginatedStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaginatedState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$PaginatedInitialImplCopyWith<T, $Res> {
  factory _$$PaginatedInitialImplCopyWith(
    _$PaginatedInitialImpl<T> value,
    $Res Function(_$PaginatedInitialImpl<T>) then,
  ) = __$$PaginatedInitialImplCopyWithImpl<T, $Res>;
}

/// @nodoc
class __$$PaginatedInitialImplCopyWithImpl<T, $Res>
    extends _$PaginatedStateCopyWithImpl<T, $Res, _$PaginatedInitialImpl<T>>
    implements _$$PaginatedInitialImplCopyWith<T, $Res> {
  __$$PaginatedInitialImplCopyWithImpl(
    _$PaginatedInitialImpl<T> _value,
    $Res Function(_$PaginatedInitialImpl<T>) _then,
  ) : super(_value, _then);

  /// Create a copy of PaginatedState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$PaginatedInitialImpl<T> implements PaginatedInitial<T> {
  const _$PaginatedInitialImpl();

  @override
  String toString() {
    return 'PaginatedState<$T>.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaginatedInitialImpl<T>);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<T> currentData) loadingMore,
    required TResult Function(List<T> data, bool hasMore, int currentPage)
    success,
    required TResult Function(Failure failure) error,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<T> currentData)? loadingMore,
    TResult? Function(List<T> data, bool hasMore, int currentPage)? success,
    TResult? Function(Failure failure)? error,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<T> currentData)? loadingMore,
    TResult Function(List<T> data, bool hasMore, int currentPage)? success,
    TResult Function(Failure failure)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PaginatedInitial<T> value) initial,
    required TResult Function(PaginatedLoading<T> value) loading,
    required TResult Function(PaginatedLoadingMore<T> value) loadingMore,
    required TResult Function(PaginatedSuccess<T> value) success,
    required TResult Function(PaginatedError<T> value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PaginatedInitial<T> value)? initial,
    TResult? Function(PaginatedLoading<T> value)? loading,
    TResult? Function(PaginatedLoadingMore<T> value)? loadingMore,
    TResult? Function(PaginatedSuccess<T> value)? success,
    TResult? Function(PaginatedError<T> value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PaginatedInitial<T> value)? initial,
    TResult Function(PaginatedLoading<T> value)? loading,
    TResult Function(PaginatedLoadingMore<T> value)? loadingMore,
    TResult Function(PaginatedSuccess<T> value)? success,
    TResult Function(PaginatedError<T> value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class PaginatedInitial<T> implements PaginatedState<T> {
  const factory PaginatedInitial() = _$PaginatedInitialImpl<T>;
}

/// @nodoc
abstract class _$$PaginatedLoadingImplCopyWith<T, $Res> {
  factory _$$PaginatedLoadingImplCopyWith(
    _$PaginatedLoadingImpl<T> value,
    $Res Function(_$PaginatedLoadingImpl<T>) then,
  ) = __$$PaginatedLoadingImplCopyWithImpl<T, $Res>;
}

/// @nodoc
class __$$PaginatedLoadingImplCopyWithImpl<T, $Res>
    extends _$PaginatedStateCopyWithImpl<T, $Res, _$PaginatedLoadingImpl<T>>
    implements _$$PaginatedLoadingImplCopyWith<T, $Res> {
  __$$PaginatedLoadingImplCopyWithImpl(
    _$PaginatedLoadingImpl<T> _value,
    $Res Function(_$PaginatedLoadingImpl<T>) _then,
  ) : super(_value, _then);

  /// Create a copy of PaginatedState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$PaginatedLoadingImpl<T> implements PaginatedLoading<T> {
  const _$PaginatedLoadingImpl();

  @override
  String toString() {
    return 'PaginatedState<$T>.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaginatedLoadingImpl<T>);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<T> currentData) loadingMore,
    required TResult Function(List<T> data, bool hasMore, int currentPage)
    success,
    required TResult Function(Failure failure) error,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<T> currentData)? loadingMore,
    TResult? Function(List<T> data, bool hasMore, int currentPage)? success,
    TResult? Function(Failure failure)? error,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<T> currentData)? loadingMore,
    TResult Function(List<T> data, bool hasMore, int currentPage)? success,
    TResult Function(Failure failure)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PaginatedInitial<T> value) initial,
    required TResult Function(PaginatedLoading<T> value) loading,
    required TResult Function(PaginatedLoadingMore<T> value) loadingMore,
    required TResult Function(PaginatedSuccess<T> value) success,
    required TResult Function(PaginatedError<T> value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PaginatedInitial<T> value)? initial,
    TResult? Function(PaginatedLoading<T> value)? loading,
    TResult? Function(PaginatedLoadingMore<T> value)? loadingMore,
    TResult? Function(PaginatedSuccess<T> value)? success,
    TResult? Function(PaginatedError<T> value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PaginatedInitial<T> value)? initial,
    TResult Function(PaginatedLoading<T> value)? loading,
    TResult Function(PaginatedLoadingMore<T> value)? loadingMore,
    TResult Function(PaginatedSuccess<T> value)? success,
    TResult Function(PaginatedError<T> value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class PaginatedLoading<T> implements PaginatedState<T> {
  const factory PaginatedLoading() = _$PaginatedLoadingImpl<T>;
}

/// @nodoc
abstract class _$$PaginatedLoadingMoreImplCopyWith<T, $Res> {
  factory _$$PaginatedLoadingMoreImplCopyWith(
    _$PaginatedLoadingMoreImpl<T> value,
    $Res Function(_$PaginatedLoadingMoreImpl<T>) then,
  ) = __$$PaginatedLoadingMoreImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({List<T> currentData});
}

/// @nodoc
class __$$PaginatedLoadingMoreImplCopyWithImpl<T, $Res>
    extends _$PaginatedStateCopyWithImpl<T, $Res, _$PaginatedLoadingMoreImpl<T>>
    implements _$$PaginatedLoadingMoreImplCopyWith<T, $Res> {
  __$$PaginatedLoadingMoreImplCopyWithImpl(
    _$PaginatedLoadingMoreImpl<T> _value,
    $Res Function(_$PaginatedLoadingMoreImpl<T>) _then,
  ) : super(_value, _then);

  /// Create a copy of PaginatedState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? currentData = null}) {
    return _then(
      _$PaginatedLoadingMoreImpl<T>(
        null == currentData
            ? _value._currentData
            : currentData // ignore: cast_nullable_to_non_nullable
                  as List<T>,
      ),
    );
  }
}

/// @nodoc

class _$PaginatedLoadingMoreImpl<T> implements PaginatedLoadingMore<T> {
  const _$PaginatedLoadingMoreImpl(final List<T> currentData)
    : _currentData = currentData;

  final List<T> _currentData;
  @override
  List<T> get currentData {
    if (_currentData is EqualUnmodifiableListView) return _currentData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_currentData);
  }

  @override
  String toString() {
    return 'PaginatedState<$T>.loadingMore(currentData: $currentData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaginatedLoadingMoreImpl<T> &&
            const DeepCollectionEquality().equals(
              other._currentData,
              _currentData,
            ));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_currentData),
  );

  /// Create a copy of PaginatedState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaginatedLoadingMoreImplCopyWith<T, _$PaginatedLoadingMoreImpl<T>>
  get copyWith =>
      __$$PaginatedLoadingMoreImplCopyWithImpl<
        T,
        _$PaginatedLoadingMoreImpl<T>
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<T> currentData) loadingMore,
    required TResult Function(List<T> data, bool hasMore, int currentPage)
    success,
    required TResult Function(Failure failure) error,
  }) {
    return loadingMore(currentData);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<T> currentData)? loadingMore,
    TResult? Function(List<T> data, bool hasMore, int currentPage)? success,
    TResult? Function(Failure failure)? error,
  }) {
    return loadingMore?.call(currentData);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<T> currentData)? loadingMore,
    TResult Function(List<T> data, bool hasMore, int currentPage)? success,
    TResult Function(Failure failure)? error,
    required TResult orElse(),
  }) {
    if (loadingMore != null) {
      return loadingMore(currentData);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PaginatedInitial<T> value) initial,
    required TResult Function(PaginatedLoading<T> value) loading,
    required TResult Function(PaginatedLoadingMore<T> value) loadingMore,
    required TResult Function(PaginatedSuccess<T> value) success,
    required TResult Function(PaginatedError<T> value) error,
  }) {
    return loadingMore(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PaginatedInitial<T> value)? initial,
    TResult? Function(PaginatedLoading<T> value)? loading,
    TResult? Function(PaginatedLoadingMore<T> value)? loadingMore,
    TResult? Function(PaginatedSuccess<T> value)? success,
    TResult? Function(PaginatedError<T> value)? error,
  }) {
    return loadingMore?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PaginatedInitial<T> value)? initial,
    TResult Function(PaginatedLoading<T> value)? loading,
    TResult Function(PaginatedLoadingMore<T> value)? loadingMore,
    TResult Function(PaginatedSuccess<T> value)? success,
    TResult Function(PaginatedError<T> value)? error,
    required TResult orElse(),
  }) {
    if (loadingMore != null) {
      return loadingMore(this);
    }
    return orElse();
  }
}

abstract class PaginatedLoadingMore<T> implements PaginatedState<T> {
  const factory PaginatedLoadingMore(final List<T> currentData) =
      _$PaginatedLoadingMoreImpl<T>;

  List<T> get currentData;

  /// Create a copy of PaginatedState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaginatedLoadingMoreImplCopyWith<T, _$PaginatedLoadingMoreImpl<T>>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PaginatedSuccessImplCopyWith<T, $Res> {
  factory _$$PaginatedSuccessImplCopyWith(
    _$PaginatedSuccessImpl<T> value,
    $Res Function(_$PaginatedSuccessImpl<T>) then,
  ) = __$$PaginatedSuccessImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({List<T> data, bool hasMore, int currentPage});
}

/// @nodoc
class __$$PaginatedSuccessImplCopyWithImpl<T, $Res>
    extends _$PaginatedStateCopyWithImpl<T, $Res, _$PaginatedSuccessImpl<T>>
    implements _$$PaginatedSuccessImplCopyWith<T, $Res> {
  __$$PaginatedSuccessImplCopyWithImpl(
    _$PaginatedSuccessImpl<T> _value,
    $Res Function(_$PaginatedSuccessImpl<T>) _then,
  ) : super(_value, _then);

  /// Create a copy of PaginatedState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? hasMore = null,
    Object? currentPage = null,
  }) {
    return _then(
      _$PaginatedSuccessImpl<T>(
        data: null == data
            ? _value._data
            : data // ignore: cast_nullable_to_non_nullable
                  as List<T>,
        hasMore: null == hasMore
            ? _value.hasMore
            : hasMore // ignore: cast_nullable_to_non_nullable
                  as bool,
        currentPage: null == currentPage
            ? _value.currentPage
            : currentPage // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc

class _$PaginatedSuccessImpl<T> implements PaginatedSuccess<T> {
  const _$PaginatedSuccessImpl({
    required final List<T> data,
    required this.hasMore,
    required this.currentPage,
  }) : _data = data;

  final List<T> _data;
  @override
  List<T> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  final bool hasMore;
  @override
  final int currentPage;

  @override
  String toString() {
    return 'PaginatedState<$T>.success(data: $data, hasMore: $hasMore, currentPage: $currentPage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaginatedSuccessImpl<T> &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.hasMore, hasMore) || other.hasMore == hasMore) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_data),
    hasMore,
    currentPage,
  );

  /// Create a copy of PaginatedState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaginatedSuccessImplCopyWith<T, _$PaginatedSuccessImpl<T>> get copyWith =>
      __$$PaginatedSuccessImplCopyWithImpl<T, _$PaginatedSuccessImpl<T>>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<T> currentData) loadingMore,
    required TResult Function(List<T> data, bool hasMore, int currentPage)
    success,
    required TResult Function(Failure failure) error,
  }) {
    return success(data, hasMore, currentPage);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<T> currentData)? loadingMore,
    TResult? Function(List<T> data, bool hasMore, int currentPage)? success,
    TResult? Function(Failure failure)? error,
  }) {
    return success?.call(data, hasMore, currentPage);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<T> currentData)? loadingMore,
    TResult Function(List<T> data, bool hasMore, int currentPage)? success,
    TResult Function(Failure failure)? error,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(data, hasMore, currentPage);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PaginatedInitial<T> value) initial,
    required TResult Function(PaginatedLoading<T> value) loading,
    required TResult Function(PaginatedLoadingMore<T> value) loadingMore,
    required TResult Function(PaginatedSuccess<T> value) success,
    required TResult Function(PaginatedError<T> value) error,
  }) {
    return success(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PaginatedInitial<T> value)? initial,
    TResult? Function(PaginatedLoading<T> value)? loading,
    TResult? Function(PaginatedLoadingMore<T> value)? loadingMore,
    TResult? Function(PaginatedSuccess<T> value)? success,
    TResult? Function(PaginatedError<T> value)? error,
  }) {
    return success?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PaginatedInitial<T> value)? initial,
    TResult Function(PaginatedLoading<T> value)? loading,
    TResult Function(PaginatedLoadingMore<T> value)? loadingMore,
    TResult Function(PaginatedSuccess<T> value)? success,
    TResult Function(PaginatedError<T> value)? error,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(this);
    }
    return orElse();
  }
}

abstract class PaginatedSuccess<T> implements PaginatedState<T> {
  const factory PaginatedSuccess({
    required final List<T> data,
    required final bool hasMore,
    required final int currentPage,
  }) = _$PaginatedSuccessImpl<T>;

  List<T> get data;
  bool get hasMore;
  int get currentPage;

  /// Create a copy of PaginatedState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaginatedSuccessImplCopyWith<T, _$PaginatedSuccessImpl<T>> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PaginatedErrorImplCopyWith<T, $Res> {
  factory _$$PaginatedErrorImplCopyWith(
    _$PaginatedErrorImpl<T> value,
    $Res Function(_$PaginatedErrorImpl<T>) then,
  ) = __$$PaginatedErrorImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({Failure failure});

  $FailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$PaginatedErrorImplCopyWithImpl<T, $Res>
    extends _$PaginatedStateCopyWithImpl<T, $Res, _$PaginatedErrorImpl<T>>
    implements _$$PaginatedErrorImplCopyWith<T, $Res> {
  __$$PaginatedErrorImplCopyWithImpl(
    _$PaginatedErrorImpl<T> _value,
    $Res Function(_$PaginatedErrorImpl<T>) _then,
  ) : super(_value, _then);

  /// Create a copy of PaginatedState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? failure = null}) {
    return _then(
      _$PaginatedErrorImpl<T>(
        null == failure
            ? _value.failure
            : failure // ignore: cast_nullable_to_non_nullable
                  as Failure,
      ),
    );
  }

  /// Create a copy of PaginatedState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FailureCopyWith<$Res> get failure {
    return $FailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$PaginatedErrorImpl<T> implements PaginatedError<T> {
  const _$PaginatedErrorImpl(this.failure);

  @override
  final Failure failure;

  @override
  String toString() {
    return 'PaginatedState<$T>.error(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaginatedErrorImpl<T> &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  /// Create a copy of PaginatedState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaginatedErrorImplCopyWith<T, _$PaginatedErrorImpl<T>> get copyWith =>
      __$$PaginatedErrorImplCopyWithImpl<T, _$PaginatedErrorImpl<T>>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<T> currentData) loadingMore,
    required TResult Function(List<T> data, bool hasMore, int currentPage)
    success,
    required TResult Function(Failure failure) error,
  }) {
    return error(failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<T> currentData)? loadingMore,
    TResult? Function(List<T> data, bool hasMore, int currentPage)? success,
    TResult? Function(Failure failure)? error,
  }) {
    return error?.call(failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<T> currentData)? loadingMore,
    TResult Function(List<T> data, bool hasMore, int currentPage)? success,
    TResult Function(Failure failure)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PaginatedInitial<T> value) initial,
    required TResult Function(PaginatedLoading<T> value) loading,
    required TResult Function(PaginatedLoadingMore<T> value) loadingMore,
    required TResult Function(PaginatedSuccess<T> value) success,
    required TResult Function(PaginatedError<T> value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PaginatedInitial<T> value)? initial,
    TResult? Function(PaginatedLoading<T> value)? loading,
    TResult? Function(PaginatedLoadingMore<T> value)? loadingMore,
    TResult? Function(PaginatedSuccess<T> value)? success,
    TResult? Function(PaginatedError<T> value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PaginatedInitial<T> value)? initial,
    TResult Function(PaginatedLoading<T> value)? loading,
    TResult Function(PaginatedLoadingMore<T> value)? loadingMore,
    TResult Function(PaginatedSuccess<T> value)? success,
    TResult Function(PaginatedError<T> value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class PaginatedError<T> implements PaginatedState<T> {
  const factory PaginatedError(final Failure failure) = _$PaginatedErrorImpl<T>;

  Failure get failure;

  /// Create a copy of PaginatedState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaginatedErrorImplCopyWith<T, _$PaginatedErrorImpl<T>> get copyWith =>
      throw _privateConstructorUsedError;
}
