import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../datasources/database.dart';

/// Provider for the main application database instance.
/// This is a singleton provider that creates and maintains the database connection.
final databaseProvider = Provider<AppDatabase>((ref) {
  final database = AppDatabase();
  
  // Ensure the database is properly disposed when the provider is disposed
  ref.onDispose(() {
    database.close();
  });
  
  return database;
});

/// Provider for database path (used for testing and configuration)
final databasePathProvider = Provider<String?>((ref) => null);

/// Provider for checking database initialization status
final databaseInitializedProvider = FutureProvider<bool>((ref) async {
  final database = ref.watch(databaseProvider);
  
  try {
    // Try to perform a simple query to check if database is initialized
    await database.customSelect('SELECT 1').getSingle();
    return true;
  } catch (e) {
    return false;
  }
});

/// Provider for database health check
final databaseHealthProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final database = ref.watch(databaseProvider);
  
  try {
    // Check if all tables exist and are accessible
    final tables = [
      'income_table',
      'orders_table',
      'performance_table',
      'spare_parts_table',
      'spare_parts_history_table',
      'level_settings_table',
      'app_settings_table',
    ];
    
    final tableStatus = <String, bool>{};
    
    for (final table in tables) {
      try {
        await database.customSelect('SELECT COUNT(*) FROM $table').getSingle();
        tableStatus[table] = true;
      } catch (e) {
        tableStatus[table] = false;
      }
    }
    
    final allTablesHealthy = tableStatus.values.every((status) => status);
    
    return {
      'healthy': allTablesHealthy,
      'tables': tableStatus,
      'timestamp': DateTime.now().toIso8601String(),
    };
  } catch (e) {
    return {
      'healthy': false,
      'error': e.toString(),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
});
