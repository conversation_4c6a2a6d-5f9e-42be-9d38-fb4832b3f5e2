import 'package:freezed_annotation/freezed_annotation.dart';

import '../errors/failures.dart';

part 'base_state.freezed.dart';

/// Base state class for handling loading, success, and error states.
/// Uses Freezed for immutable state management with Riverpod.
@freezed
class BaseState<T> with _$BaseState<T> {
  const factory BaseState.initial() = Initial<T>;
  const factory BaseState.loading() = Loading<T>;
  const factory BaseState.success(T data) = Success<T>;
  const factory BaseState.error(Failure failure) = Error<T>;
}

/// Extension methods for BaseState
extension BaseStateExtension<T> on BaseState<T> {
  /// Check if the state is initial
  bool get isInitial => when(
        initial: () => true,
        loading: () => false,
        success: (_) => false,
        error: (_) => false,
      );

  /// Check if the state is loading
  bool get isLoading => when(
        initial: () => false,
        loading: () => true,
        success: (_) => false,
        error: (_) => false,
      );

  /// Check if the state is successful
  bool get isSuccess => when(
        initial: () => false,
        loading: () => false,
        success: (_) => true,
        error: (_) => false,
      );

  /// Check if the state is in error
  bool get isError => when(
        initial: () => false,
        loading: () => false,
        success: (_) => false,
        error: (_) => true,
      );

  /// Get the data if successful, otherwise return null
  T? get dataOrNull => when(
        initial: () => null,
        loading: () => null,
        success: (data) => data,
        error: (_) => null,
      );

  /// Get the failure if in error state, otherwise return null
  Failure? get failureOrNull => when(
        initial: () => null,
        loading: () => null,
        success: (_) => null,
        error: (failure) => failure,
      );

  /// Transform the data if successful
  BaseState<R> map<R>(R Function(T data) transform) {
    return when(
      initial: () => const BaseState.initial(),
      loading: () => const BaseState.loading(),
      success: (data) => BaseState.success(transform(data)),
      error: (failure) => BaseState.error(failure),
    );
  }

  /// Execute a side effect if successful
  BaseState<T> onSuccess(void Function(T data) action) {
    when(
      initial: () {},
      loading: () {},
      success: (data) => action(data),
      error: (_) {},
    );
    return this;
  }

  /// Execute a side effect if in error state
  BaseState<T> onError(void Function(Failure failure) action) {
    when(
      initial: () {},
      loading: () {},
      success: (_) {},
      error: (failure) => action(failure),
    );
    return this;
  }

  /// Execute a side effect if loading
  BaseState<T> onLoading(void Function() action) {
    when(
      initial: () {},
      loading: () => action(),
      success: (_) {},
      error: (_) {},
    );
    return this;
  }
}

/// Paginated state for handling lists with pagination
@freezed
class PaginatedState<T> with _$PaginatedState<T> {
  const factory PaginatedState.initial() = PaginatedInitial<T>;
  const factory PaginatedState.loading() = PaginatedLoading<T>;
  const factory PaginatedState.loadingMore(List<T> currentData) = PaginatedLoadingMore<T>;
  const factory PaginatedState.success({
    required List<T> data,
    required bool hasMore,
    required int currentPage,
  }) = PaginatedSuccess<T>;
  const factory PaginatedState.error(Failure failure) = PaginatedError<T>;
}

/// Extension methods for PaginatedState
extension PaginatedStateExtension<T> on PaginatedState<T> {
  /// Check if the state is loading (initial or more)
  bool get isLoading => when(
        initial: () => false,
        loading: () => true,
        loadingMore: (_) => true,
        success: (_, __, ___) => false,
        error: (_) => false,
      );

  /// Check if the state is successful
  bool get isSuccess => when(
        initial: () => false,
        loading: () => false,
        loadingMore: (_) => false,
        success: (_, __, ___) => true,
        error: (_) => false,
      );

  /// Get the current data
  List<T> get dataOrEmpty => when(
        initial: () => <T>[],
        loading: () => <T>[],
        loadingMore: (currentData) => currentData,
        success: (data, _, __) => data,
        error: (_) => <T>[],
      );

  /// Check if there's more data to load
  bool get hasMore => when(
        initial: () => false,
        loading: () => false,
        loadingMore: (_) => true,
        success: (_, hasMore, __) => hasMore,
        error: (_) => false,
      );

  /// Get the current page
  int get currentPage => when(
        initial: () => 0,
        loading: () => 0,
        loadingMore: (_) => 0,
        success: (_, __, currentPage) => currentPage,
        error: (_) => 0,
      );
}
