import 'package:freezed_annotation/freezed_annotation.dart';

part 'failures.freezed.dart';

/// Base failure class for handling errors throughout the application.
/// Uses Freezed for immutable error handling with different failure types.
@freezed
class Failure with _$Failure {
  const factory Failure.database(String message) = DatabaseFailure;
  const factory Failure.network(String message) = NetworkFailure;
  const factory Failure.validation(String message) = ValidationFailure;
  const factory Failure.notFound(String message) = NotFoundFailure;
  const factory Failure.businessLogic(String message) = BusinessLogicFailure;
  const factory Failure.authentication(String message) = AuthenticationFailure;
  const factory Failure.authorization(String message) = AuthorizationFailure;
  const factory Failure.sync(String message) = SyncFailure;
  const factory Failure.storage(String message) = StorageFailure;
  const factory Failure.parsing(String message) = ParsingFailure;
  const factory Failure.timeout(String message) = TimeoutFailure;
  const factory Failure.unknown(String message) = UnknownFailure;
}

/// Extension to provide user-friendly error messages
extension FailureExtension on Failure {
  String get userMessage {
    return when(
      database: (message) => 'Database error occurred. Please try again.',
      network: (message) => 'Network connection error. Please check your internet connection.',
      validation: (message) => message,
      notFound: (message) => 'Requested data not found.',
      businessLogic: (message) => message,
      authentication: (message) => 'Authentication failed. Please login again.',
      authorization: (message) => 'You are not authorized to perform this action.',
      sync: (message) => 'Synchronization failed. Please try again later.',
      storage: (message) => 'Storage error occurred. Please try again.',
      parsing: (message) => 'Data parsing error occurred.',
      timeout: (message) => 'Operation timed out. Please try again.',
      unknown: (message) => 'An unexpected error occurred. Please try again.',
    );
  }

  String get technicalMessage {
    return when(
      database: (message) => 'Database: $message',
      network: (message) => 'Network: $message',
      validation: (message) => 'Validation: $message',
      notFound: (message) => 'NotFound: $message',
      businessLogic: (message) => 'BusinessLogic: $message',
      authentication: (message) => 'Authentication: $message',
      authorization: (message) => 'Authorization: $message',
      sync: (message) => 'Sync: $message',
      storage: (message) => 'Storage: $message',
      parsing: (message) => 'Parsing: $message',
      timeout: (message) => 'Timeout: $message',
      unknown: (message) => 'Unknown: $message',
    );
  }

  bool get isRetryable {
    return when(
      database: (_) => true,
      network: (_) => true,
      validation: (_) => false,
      notFound: (_) => false,
      businessLogic: (_) => false,
      authentication: (_) => false,
      authorization: (_) => false,
      sync: (_) => true,
      storage: (_) => true,
      parsing: (_) => false,
      timeout: (_) => true,
      unknown: (_) => true,
    );
  }
}
