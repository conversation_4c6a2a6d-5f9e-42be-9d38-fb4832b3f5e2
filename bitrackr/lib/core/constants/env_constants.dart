/// Environment constants for the bitrackr application.
/// These constants define environment variable keys and default values.
class EnvConstants {
  EnvConstants._();

  // Supabase Configuration Keys
  static const String supabaseUrlKey = 'SUPABASE_URL';
  static const String supabaseAnonKeyKey = 'SUPABASE_ANON_KEY';
  static const String supabaseServiceRoleKeyKey = 'SUPABASE_SERVICE_ROLE_KEY';

  // App Configuration Keys
  static const String appNameKey = 'APP_NAME';
  static const String appVersionKey = 'APP_VERSION';
  static const String databaseNameKey = 'DATABASE_NAME';

  // Debug Configuration Keys
  static const String debugModeKey = 'DEBUG_MODE';
  static const String enableLoggingKey = 'ENABLE_LOGGING';

  // Default Values
  static const String defaultAppName = 'bitrackr';
  static const String defaultAppVersion = '1.0.0';
  static const String defaultDatabaseName = 'bitrackr.db';
  static const bool defaultDebugMode = true;
  static const bool defaultEnableLogging = true;

  // Environment File
  static const String envFileName = '.env';
  static const String envExampleFileName = '.env.example';
}
