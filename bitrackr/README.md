# bitrackr - Driver Performance Tracker

A comprehensive Flutter application for driver performance tracking and income management, designed specifically for ride-sharing and delivery drivers.

## 📱 Overview

bitrackr is a powerful mobile application that helps drivers track their daily performance, manage income, monitor vehicle maintenance, and analyze their driving patterns. Built with Flutter and following clean architecture principles, it provides a robust and scalable solution for professional drivers.

## ✨ Features

### 📊 Performance Analytics
- Real-time performance tracking
- Bid acceptance rate monitoring
- Trip completion rate analysis
- Daily, weekly, and monthly reports
- Level-based achievement system (Platinum, Gold, Silver)

### 💰 Income Management
- Multi-payment method tracking (GoPay, BCA, Cash, OVO, BRI, RekPon)
- Automatic net income calculations
- Mileage tracking and analysis
- Capital and result calculations
- Comprehensive financial reports

### 🚗 Vehicle Maintenance
- Spare parts tracking and management
- Maintenance schedule reminders
- Parts replacement history
- Cost analysis and budgeting
- Warning alerts for upcoming maintenance

### 📈 Data Visualization
- Interactive charts and graphs
- Performance trend analysis
- Income distribution charts
- Maintenance cost tracking
- Custom date range filtering

### ☁️ Cloud Sync & Backup
- Secure cloud synchronization
- Automatic data backup
- Multi-device support
- Offline-first architecture
- Data export capabilities

## 🏗️ Architecture

This project follows **Clean Architecture** principles with the following structure:

```
lib/
├── core/                    # Core functionality
│   ├── components/          # Reusable UI components
│   ├── config/             # App configuration
│   ├── constants/          # App constants
│   ├── datasources/        # Database layer
│   ├── di/                 # Dependency injection
│   ├── errors/             # Error handling
│   ├── models/             # Data models
│   ├── providers/          # State management
│   ├── repositories/       # Data repositories
│   ├── services/           # Business logic
│   ├── theme/              # UI theming
│   ├── utils/              # Utility functions
│   └── widgets/            # Shared widgets
└── features/               # Feature modules
    ├── auth/               # Authentication
    ├── home/               # Main navigation
    ├── income/             # Income tracking
    ├── orders/             # Order management
    ├── performance/        # Performance analytics
    ├── spare_parts/        # Spare parts management
    ├── levels/             # Level system
    ├── backup/             # Backup & restore
    ├── settings/           # App settings
    └── sync/               # Cloud sync
```

## 🛠️ Technology Stack

- **Framework**: Flutter 3.x
- **State Management**: Riverpod 2.x
- **Database**: Drift (SQLite)
- **Backend**: Supabase
- **Code Generation**: Freezed, JSON Annotation
- **Charts**: FL Chart
- **Typography**: Google Fonts
- **Architecture**: Clean Architecture with MVVM

## 🚀 Getting Started

### Prerequisites

- Flutter SDK (3.16.0 or higher)
- Dart SDK (3.2.0 or higher)
- Android Studio / VS Code
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/bitrackr.git
   cd bitrackr
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env file with your Supabase credentials
   ```

4. **Generate code**
   ```bash
   dart run build_runner build --delete-conflicting-outputs
   ```

5. **Run the application**
   ```bash
   flutter run
   ```

### Environment Configuration

Create a `.env` file in the root directory with the following variables:

```env
# Supabase Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key_here

# App Configuration
APP_NAME=bitrackr
APP_VERSION=1.0.0
DATABASE_NAME=bitrackr.db

# Debug Configuration
DEBUG_MODE=true
ENABLE_LOGGING=true
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
flutter test

# Run tests with coverage
flutter test --coverage

# Run integration tests
flutter test integration_test/
```

### Test Structure

- **Unit Tests**: `test/unit/`
- **Widget Tests**: `test/widget/`
- **Integration Tests**: `integration_test/`

## 📦 Build & Deployment

### Android

```bash
# Build APK
flutter build apk --release

# Build App Bundle
flutter build appbundle --release
```

### iOS

```bash
# Build iOS
flutter build ios --release
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow the established clean architecture structure
- Write comprehensive tests for new features
- Use conventional commit messages
- Ensure code passes all linting rules
- Update documentation for significant changes

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Flutter team for the amazing framework
- Riverpod for excellent state management
- Supabase for backend services
- All contributors and testers

## 📞 Support

For support, email <EMAIL> or join our [Discord community](https://discord.gg/bitrackr).

---

**Made with ❤️ for professional drivers worldwide**
